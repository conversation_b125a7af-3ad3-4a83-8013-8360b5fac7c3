/**
 * Phase 2B API Validation Script
 * Tests Phase 2B optimizations through API endpoints
 */

const { performance } = require('perf_hooks');
const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://localhost:3000';

class Phase2BAPIValidator {
  constructor() {
    this.results = [];
    this.totalTests = 0;
    this.passedTests = 0;
  }

  async runValidation() {
    console.log('🚀 Starting Phase 2B API Validation\n');
    console.log('Testing Phase 2B optimizations through API endpoints...\n');

    try {
      // Start the development server first
      console.log('📡 Checking if development server is running...');
      const serverRunning = await this.checkServerStatus();
      
      if (!serverRunning) {
        console.log('⚠️  Development server not running. Please start with: npm run dev');
        console.log('   Proceeding with offline validation tests...\n');
      }

      // Test 1: Pagination API Performance
      await this.testPaginationAPI();
      
      // Test 2: Career Path Data Optimization
      await this.testCareerPathDataAPI();
      
      // Test 3: Skills Analysis Performance
      await this.testSkillsAnalysisAPI();
      
      // Test 4: Database Query Optimization
      await this.testDatabaseQueryOptimization();
      
      // Test 5: Cache Performance
      await this.testCachePerformance();
      
      // Test 6: Concurrent Request Handling
      await this.testConcurrentRequestHandling();

      this.printValidationSummary();
      
    } catch (error) {
      console.error('❌ Phase 2B API validation failed:', error);
      process.exit(1);
    }
  }

  async checkServerStatus() {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/health`, { timeout: 5000 });
      console.log('   ✅ Development server is running\n');
      return true;
    } catch (error) {
      return false;
    }
  }

  async testPaginationAPI() {
    console.log('📄 Testing Pagination API Performance...');
    
    const startTime = performance.now();
    
    try {
      // Test career paths pagination
      const paginationTests = [
        { endpoint: '/api/career-paths', params: { page: 1, limit: 10 } },
        { endpoint: '/api/career-paths', params: { page: 2, limit: 5 } },
        { endpoint: '/api/learning-resources', params: { page: 1, limit: 20 } }
      ];

      let allPassed = true;
      
      for (const test of paginationTests) {
        try {
          const testStart = performance.now();
          const response = await axios.get(`${API_BASE_URL}${test.endpoint}`, {
            params: test.params,
            timeout: 5000
          });
          const testEnd = performance.now();
          const responseTime = testEnd - testStart;
          
          console.log(`   ✅ ${test.endpoint}: ${responseTime.toFixed(2)}ms (${response.data?.data?.length || 0} items)`);
          
          // Check if pagination metadata is present
          if (response.data?.pagination) {
            console.log(`      📊 Pagination: page ${response.data.pagination.page}, total ${response.data.pagination.total}`);
          }
          
        } catch (error) {
          console.log(`   ❌ ${test.endpoint}: ${error.message}`);
          allPassed = false;
        }
      }
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      this.recordTestResult('Pagination API Performance', totalTime, 500, allPassed && totalTime < 500);
      
      console.log(`   📊 Total pagination tests: ${totalTime.toFixed(2)}ms\n`);
      
    } catch (error) {
      this.recordTestResult('Pagination API Performance', 0, 500, false, error.message);
      console.log(`   ❌ Pagination API test failed: ${error.message}\n`);
    }
  }

  async testCareerPathDataAPI() {
    console.log('🛤️ Testing Career Path Data API Optimization...');
    
    const startTime = performance.now();
    
    try {
      // Test enhanced career path data retrieval
      const response = await axios.get(`${API_BASE_URL}/api/career-paths`, { timeout: 10000 });
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      const passed = responseTime < 1000 && response.status === 200;
      
      console.log(`   ✅ Career paths loaded in ${responseTime.toFixed(2)}ms`);
      console.log(`   📊 Data: ${response.data?.data?.length || 0} career paths`);
      
      // Test individual career path with enhanced data
      if (response.data?.data?.length > 0) {
        const firstPath = response.data.data[0];
        const detailStart = performance.now();
        
        try {
          const detailResponse = await axios.get(`${API_BASE_URL}/api/career-paths/${firstPath.id}`, { timeout: 5000 });
          const detailEnd = performance.now();
          const detailTime = detailEnd - detailStart;
          
          console.log(`   ✅ Career path details loaded in ${detailTime.toFixed(2)}ms`);
          console.log(`   📊 Enhanced data: ${Object.keys(detailResponse.data || {}).length} fields`);
          
        } catch (error) {
          console.log(`   ⚠️  Career path details test skipped: ${error.message}`);
        }
      }
      
      this.recordTestResult('Career Path Data API', responseTime, 1000, passed);
      
    } catch (error) {
      this.recordTestResult('Career Path Data API', 0, 1000, false, error.message);
      console.log(`   ❌ Career path data test failed: ${error.message}`);
    }
    
    console.log('');
  }

  async testSkillsAnalysisAPI() {
    console.log('🎯 Testing Skills Analysis API Performance...');
    
    const startTime = performance.now();
    
    try {
      // Test skills search (should use optimized queries)
      const searchResponse = await axios.get(`${API_BASE_URL}/api/skills/search`, {
        params: { q: 'javascript' },
        timeout: 5000
      });
      
      const searchTime = performance.now() - startTime;
      
      console.log(`   ✅ Skills search completed in ${searchTime.toFixed(2)}ms`);
      console.log(`   📊 Results: ${searchResponse.data?.length || 0} skills found`);
      
      // Test health endpoint for performance monitoring
      const healthStart = performance.now();
      const healthResponse = await axios.get(`${API_BASE_URL}/api/health`, { timeout: 3000 });
      const healthTime = performance.now() - healthStart;
      
      console.log(`   ✅ Health check completed in ${healthTime.toFixed(2)}ms`);
      console.log(`   📊 Status: ${healthResponse.data?.status || 'unknown'}`);
      
      const totalTime = performance.now() - startTime;
      const passed = totalTime < 2000 && searchResponse.status === 200;
      
      this.recordTestResult('Skills Analysis API', totalTime, 2000, passed);
      
    } catch (error) {
      this.recordTestResult('Skills Analysis API', 0, 2000, false, error.message);
      console.log(`   ❌ Skills analysis test failed: ${error.message}`);
    }
    
    console.log('');
  }

  async testDatabaseQueryOptimization() {
    console.log('🗄️ Testing Database Query Optimization...');
    
    const startTime = performance.now();
    
    try {
      // Test multiple endpoints that should benefit from query optimization
      const queryTests = [
        { name: 'Learning Resources', endpoint: '/api/learning-resources' },
        { name: 'Forum Categories', endpoint: '/api/forum/categories' },
        { name: 'Test Database', endpoint: '/api/test-db' }
      ];

      let allPassed = true;
      const results = [];
      
      for (const test of queryTests) {
        try {
          const testStart = performance.now();
          const response = await axios.get(`${API_BASE_URL}${test.endpoint}`, { timeout: 5000 });
          const testTime = performance.now() - testStart;
          
          results.push({ name: test.name, time: testTime, success: true });
          console.log(`   ✅ ${test.name}: ${testTime.toFixed(2)}ms`);
          
        } catch (error) {
          results.push({ name: test.name, time: 0, success: false });
          console.log(`   ⚠️  ${test.name}: ${error.message}`);
          // Don't fail the entire test for individual endpoint failures
        }
      }
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const successfulTests = results.filter(r => r.success).length;
      
      console.log(`   📊 Database queries: ${successfulTests}/${queryTests.length} successful`);
      console.log(`   📊 Total time: ${totalTime.toFixed(2)}ms`);
      
      this.recordTestResult('Database Query Optimization', totalTime, 3000, successfulTests > 0 && totalTime < 3000);
      
    } catch (error) {
      this.recordTestResult('Database Query Optimization', 0, 3000, false, error.message);
      console.log(`   ❌ Database query test failed: ${error.message}`);
    }
    
    console.log('');
  }

  async testCachePerformance() {
    console.log('💾 Testing Cache Performance...');
    
    try {
      // Test the same endpoint multiple times to check caching
      const endpoint = '/api/career-paths';
      const iterations = 3;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        try {
          const start = performance.now();
          await axios.get(`${API_BASE_URL}${endpoint}`, { timeout: 5000 });
          const time = performance.now() - start;
          times.push(time);
          
          console.log(`   Request ${i + 1}: ${time.toFixed(2)}ms`);
          
        } catch (error) {
          console.log(`   Request ${i + 1}: Failed - ${error.message}`);
          times.push(5000); // Penalty time for failed requests
        }
      }
      
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const improvement = times.length > 1 ? ((times[0] - times[times.length - 1]) / times[0] * 100) : 0;
      
      console.log(`   📊 Average response time: ${avgTime.toFixed(2)}ms`);
      console.log(`   📊 Cache improvement: ${improvement.toFixed(1)}%`);
      
      const passed = avgTime < 1000;
      this.recordTestResult('Cache Performance', avgTime, 1000, passed);
      
    } catch (error) {
      this.recordTestResult('Cache Performance', 0, 1000, false, error.message);
      console.log(`   ❌ Cache performance test failed: ${error.message}`);
    }
    
    console.log('');
  }

  async testConcurrentRequestHandling() {
    console.log('⚡ Testing Concurrent Request Handling...');
    
    const startTime = performance.now();
    
    try {
      // Test concurrent requests to different endpoints
      const concurrentRequests = [
        axios.get(`${API_BASE_URL}/api/career-paths`, { timeout: 10000 }),
        axios.get(`${API_BASE_URL}/api/learning-resources`, { timeout: 10000 }),
        axios.get(`${API_BASE_URL}/api/forum/categories`, { timeout: 10000 }),
        axios.get(`${API_BASE_URL}/api/health`, { timeout: 10000 })
      ];
      
      const results = await Promise.allSettled(concurrentRequests);
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      console.log(`   ✅ Concurrent requests completed in ${totalTime.toFixed(2)}ms`);
      console.log(`   📊 Results: ${successful} successful, ${failed} failed`);
      
      // Log individual results
      results.forEach((result, index) => {
        const endpoints = ['/api/career-paths', '/api/learning-resources', '/api/forum/categories', '/api/health'];
        if (result.status === 'fulfilled') {
          console.log(`   ✅ ${endpoints[index]}: Success`);
        } else {
          console.log(`   ❌ ${endpoints[index]}: ${result.reason.message}`);
        }
      });
      
      const passed = successful >= 2 && totalTime < 5000; // At least 2 successful requests under 5s
      this.recordTestResult('Concurrent Request Handling', totalTime, 5000, passed);
      
    } catch (error) {
      this.recordTestResult('Concurrent Request Handling', 0, 5000, false, error.message);
      console.log(`   ❌ Concurrent request test failed: ${error.message}`);
    }
    
    console.log('');
  }

  recordTestResult(testName, executionTime, target, passed, error = null) {
    this.totalTests++;
    if (passed) this.passedTests++;
    
    this.results.push({
      testName,
      executionTime,
      target,
      passed,
      error,
      improvement: target > 0 ? ((target - executionTime) / target * 100).toFixed(1) : 'N/A'
    });
  }

  printValidationSummary() {
    console.log('📊 PHASE 2B API VALIDATION SUMMARY');
    console.log('='.repeat(70));
    console.log(`Total Tests: ${this.totalTests}`);
    console.log(`Passed: ${this.passedTests}`);
    console.log(`Failed: ${this.totalTests - this.passedTests}`);
    console.log(`Success Rate: ${(this.passedTests / this.totalTests * 100).toFixed(1)}%\n`);
    
    console.log('📈 API PERFORMANCE RESULTS:');
    console.log('-'.repeat(70));
    
    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      const improvement = result.improvement !== 'N/A' ? `(${result.improvement}% under target)` : '';
      
      if (result.target > 0) {
        console.log(`${status} ${result.testName}: ${result.executionTime.toFixed(2)}ms / ${result.target}ms ${improvement}`);
      } else {
        console.log(`${status} ${result.testName}: ${result.passed ? 'PASSED' : 'FAILED'}`);
      }
      
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    console.log('\n🎯 PHASE 2B API OPTIMIZATIONS TESTED:');
    console.log('-'.repeat(70));
    console.log('• Pagination API: Response times and metadata');
    console.log('• Career Path Data: Enhanced data loading performance');
    console.log('• Skills Analysis: Search and query optimization');
    console.log('• Database Queries: Multiple endpoint performance');
    console.log('• Cache Performance: Response time improvements');
    console.log('• Concurrent Handling: Multiple simultaneous requests');
    
    if (this.passedTests === this.totalTests) {
      console.log('\n🎉 ALL PHASE 2B API OPTIMIZATIONS VALIDATED!');
      console.log('Phase 2B optimizations are performing well through API endpoints');
    } else if (this.passedTests > this.totalTests / 2) {
      console.log('\n✅ PHASE 2B API OPTIMIZATIONS MOSTLY VALIDATED');
      console.log('Most optimizations are working, some may need server to be running');
    } else {
      console.log('\n⚠️  PHASE 2B API OPTIMIZATIONS NEED ATTENTION');
      console.log('Consider starting the development server and retesting');
    }
  }
}

// Run the API validation
const validator = new Phase2BAPIValidator();
validator.runValidation().catch(console.error);
